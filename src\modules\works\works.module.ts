import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Work, Chapter } from "./models/entities";
import { WorkRepository, ChapterRepository } from "./repositories";
import { CreateWorkUseCase, GetWorkUseCase, ListWorksUseCase, UpdateWorkUseCase, DeleteWorkUseCase, CreateChapterUseCase } from "./use-cases";
import { WorksController } from "./controllers";
import { PermissionsModule } from "../permissions/permissions.module";

@Module({
	imports: [TypeOrmModule.forFeature([Work, Chapter]), PermissionsModule],
	controllers: [WorksController],
	providers: [
		WorkRepository,
		ChapterRepository,
		CreateWorkUseCase,
		GetWorkUseCase,
		ListWorksUseCase,
		UpdateWorkUseCase,
		DeleteWorkUseCase,
		CreateChapterUseCase,
		{
			provide: "IWorkRepository",
			useExisting: WorkRepository,
		},
		{
			provide: "IChapterRepository",
			useExisting: ChapterRepository,
		},
	],
	exports: [WorkRepository, ChapterRepository, "IWorkRepository", "IChapterRepository"],
})
export class WorksModule {}
