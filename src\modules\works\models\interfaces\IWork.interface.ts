import { WorkType, WorkStatus } from "../enums";

export interface IWork {
	id: string;
	title: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	type: WorkType;
	status: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
	averageRating?: number;
	totalReviews: number;
	createdAt: Date;
	updatedAt: Date;
}

export interface ICreateWorkRequest {
	title: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	type: WorkType;
	status: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
}

export interface IUpdateWorkRequest {
	title?: string;
	originalTitle?: string;
	description?: string;
	coverImage?: string;
	status?: WorkStatus;
	author?: string;
	artist?: string;
	totalChapters?: number;
	releaseDate?: Date;
}

export interface IWorkFilters {
	type?: WorkType;
	status?: WorkStatus;
	author?: string;
	artist?: string;
	search?: string;
	tags?: string[];
	minRating?: number;
	maxRating?: number;
	page?: number;
	limit?: number;
	sortBy?: "title" | "releaseDate" | "averageRating" | "createdAt";
	sortOrder?: "ASC" | "DESC";
}

export interface IWorkRepository {
	create(data: ICreateWorkRequest): Promise<IWork>;
	findById(id: string): Promise<IWork | null>;
	findAll(filters: IWorkFilters): Promise<{ works: IWork[]; total: number }>;
	update(id: string, data: IUpdateWorkRequest): Promise<IWork>;
	delete(id: string): Promise<void>;
	findByTitle(title: string): Promise<IWork | null>;
	updateAverageRating(id: string): Promise<void>;
}
