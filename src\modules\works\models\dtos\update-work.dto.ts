import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsString, IsEnum, IsOptional, IsInt, IsDateString, MaxLength, Min, IsUrl } from "class-validator";
import { WorkStatus } from "../enums";

export class UpdateWorkDto {
	@ApiPropertyOptional({
		description: "Título da obra",
		example: "Solo Leveling",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	title?: string;

	@ApiPropertyOptional({
		description: "Título original da obra",
		example: "나 혼자만 레벨업",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	originalTitle?: string;

	@ApiPropertyOptional({
		description: "Descrição da obra",
		example: "Um caçador fraco se torna o mais forte...",
	})
	@IsOptional()
	@IsString()
	description?: string;

	@ApiPropertyOptional({
		description: "URL da imagem de capa",
		example: "https://example.com/cover.jpg",
	})
	@IsOptional()
	@IsUrl()
	coverImage?: string;

	@ApiPropertyOptional({
		description: "Status da obra",
		enum: WorkStatus,
		example: WorkStatus.COMPLETED,
	})
	@IsOptional()
	@IsEnum(WorkStatus)
	status?: WorkStatus;

	@ApiPropertyOptional({
		description: "Autor da obra",
		example: "Chugong",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	author?: string;

	@ApiPropertyOptional({
		description: "Artista da obra",
		example: "DUBU (REDICE STUDIO)",
		maxLength: 255,
	})
	@IsOptional()
	@IsString()
	@MaxLength(255)
	artist?: string;

	@ApiPropertyOptional({
		description: "Total de capítulos",
		example: 179,
		minimum: 1,
	})
	@IsOptional()
	@IsInt()
	@Min(1)
	totalChapters?: number;

	@ApiPropertyOptional({
		description: "Data de lançamento",
		example: "2018-07-25",
	})
	@IsOptional()
	@IsDateString()
	releaseDate?: Date;
}
